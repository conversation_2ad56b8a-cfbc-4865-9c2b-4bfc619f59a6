<?php
namespace BooklyPro\Frontend\Modules\CustomSearchForm;

use Bookly\Lib as BooklyLib;
use Bookly\Lib\Entities;

/**
 * Ajax handlers for Custom Search Form
 */
class Ajax extends BooklyLib\Base\Ajax
{
    /**
     * Debug counters
     */
    private static $debug_services_checked = 0;
    private static $debug_staff_checked = 0;

    /**
     * @inheritDoc
     */
    protected static function permissions()
    {
        return array( '_default' => 'anonymous' );
    }
    
    /**
     * Search for available services based on date, time, and duration
     */
    public static function searchAvailability()
    {
        // Verify nonce
        if ( ! wp_verify_nonce( self::parameter( 'nonce' ), 'bookly_custom_search_nonce' ) ) {
            wp_send_json_error( array( 'message' => 'Invalid nonce' ) );
            return;
        }

        $date = self::parameter( 'date' );
        $time = self::parameter( 'time' );
        $duration = (int) self::parameter( 'duration' );

        // Debug logging
        if ( defined( 'BOOKLY_CUSTOM_SEARCH_DEBUG' ) && BOOKLY_CUSTOM_SEARCH_DEBUG ) {
            error_log( sprintf( 'Custom Search Request - Date: %s, Time: %s, Duration: %d', $date, $time, $duration ) );
        }

        // Validate parameters
        if ( empty( $date ) || empty( $time ) || $duration <= 0 ) {
            wp_send_json_error( array( 'message' => 'Please fill in all required fields.' ) );
            return;
        }

        // Convert and validate date format
        $normalized_date = self::normalizeDateFormat( $date );
        if ( ! $normalized_date ) {
            wp_send_json_error( array( 'message' => 'Invalid date format. Please select a valid date.' ) );
            return;
        }
        $date = $normalized_date;

        // Validate time format
        if ( ! preg_match( '/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/', $time ) ) {
            wp_send_json_error( array( 'message' => 'Invalid time format.' ) );
            return;
        }

        // Validate duration range
        if ( $duration < 1 || $duration > 24 ) {
            wp_send_json_error( array( 'message' => 'Duration must be between 1 and 24 hours.' ) );
            return;
        }

        try {
            // Create datetime string
            $datetime = $date . ' ' . $time;
            $start_datetime = date_create( $datetime );

            if ( ! $start_datetime ) {
                wp_send_json_error( array( 'message' => 'Invalid date/time combination.' ) );
                return;
            }

            // Check if date is not in the past
            $now = new \DateTime();
            if ( $start_datetime < $now ) {
                wp_send_json_error( array( 'message' => 'Cannot book appointments in the past.' ) );
                return;
            }

            // Check if date is within booking limits
            $max_days = BooklyLib\Config::getMaximumAvailableDaysForBooking();
            $max_date = clone $now;
            $max_date->modify( '+' . $max_days . ' days' );

            if ( $start_datetime > $max_date ) {
                wp_send_json_error( array( 'message' => sprintf( 'Cannot book more than %d days in advance.', $max_days ) ) );
                return;
            }

            // Get available services
            $available_services = self::getAvailableServices( $start_datetime, $duration );

            // Log search for debugging (if debug mode is enabled)
            if ( defined( 'BOOKLY_CUSTOM_SEARCH_DEBUG' ) && BOOKLY_CUSTOM_SEARCH_DEBUG ) {
                error_log( sprintf(
                    'Custom Search: %s %s for %dh - Found %d services',
                    $date, $time, $duration, count( $available_services )
                ) );
            }

            wp_send_json_success( array(
                'services' => $available_services,
                'search_params' => array(
                    'date' => $date,
                    'time' => $time,
                    'duration' => $duration,
                    'datetime' => $start_datetime->format( 'Y-m-d H:i:s' )
                ),
                'debug' => defined( 'BOOKLY_CUSTOM_SEARCH_DEBUG' ) && BOOKLY_CUSTOM_SEARCH_DEBUG ? array(
                    'total_services_checked' => self::$debug_services_checked ?? 0,
                    'total_staff_checked' => self::$debug_staff_checked ?? 0,
                ) : null
            ) );

        } catch ( \Exception $e ) {
            // Log error for debugging
            if ( defined( 'BOOKLY_CUSTOM_SEARCH_DEBUG' ) && BOOKLY_CUSTOM_SEARCH_DEBUG ) {
                error_log( 'Custom Search Error: ' . $e->getMessage() );
            }

            wp_send_json_error( array( 'message' => 'An error occurred while searching. Please try again.' ) );
        }
    }
    
    /**
     * Get available services for the given datetime and duration
     *
     * @param \DateTime $start_datetime
     * @param int $duration_hours
     * @return array
     */
    private static function getAvailableServices( $start_datetime, $duration_hours )
    {
        $available_services = array();

        // Reset debug counters
        self::$debug_services_checked = 0;
        self::$debug_staff_checked = 0;

        // Get all active services and staff combinations
        $staff_services = Entities\StaffService::query( 'ss' )
            ->select( 'ss.staff_id, ss.service_id, ss.price, s.full_name as staff_name, srv.title as service_title, srv.duration as service_duration' )
            ->leftJoin( 'Staff', 's', 's.id = ss.staff_id' )
            ->leftJoin( 'Service', 'srv', 'srv.id = ss.service_id' )
            ->where( 's.visibility', 'visible' )
            ->where( 'srv.visibility', 'visible' )
            ->fetchArray();

        foreach ( $staff_services as $staff_service ) {
            self::$debug_services_checked++;
            self::$debug_staff_checked++;

            $service_id = $staff_service['service_id'];
            $staff_id = $staff_service['staff_id'];
            $service_duration_seconds = $staff_service['service_duration'];

            // Check if this combination is available at the requested time
            if ( self::checkAvailabilityWithScheduler( $staff_id, $service_id, $start_datetime ) ) {
                $end_datetime = clone $start_datetime;
                $end_datetime->modify( '+' . $service_duration_seconds . ' seconds' );

                $available_services[] = array(
                    'service_id' => $service_id,
                    'service_title' => $staff_service['service_title'],
                    'staff_id' => $staff_id,
                    'staff_name' => $staff_service['staff_name'],
                    'price' => BooklyLib\Utils\Price::format( $staff_service['price'] ),
                    'duration' => round( $service_duration_seconds / 3600, 1 ),
                    'duration_formatted' => self::formatDuration( $service_duration_seconds / 3600 ),
                    'start_time' => $start_datetime->format( 'H:i' ),
                    'end_time' => $end_datetime->format( 'H:i' ),
                    'booking_url' => self::generateBookingUrl( $service_id, $staff_id, $start_datetime->format( 'Y-m-d H:i:s' ) )
                );
            }
        }

        return $available_services;
    }
    
    /**
     * Check if staff member is available at the given time
     *
     * @param int $staff_id
     * @param int $service_id
     * @param \DateTime $start_datetime
     * @param int $duration_seconds
     * @return bool
     */
    private static function checkAvailabilityWithScheduler( $staff_id, $service_id, $start_datetime )
    {
        try {
            // Create a chain item exactly like the original Bookly form
            $chain_item = new BooklyLib\ChainItem();
            $chain_item
                ->setStaffIds( array( $staff_id ) )
                ->setServiceId( $service_id )
                ->setNumberOfPersons( 1 )
                ->setQuantity( 1 )
                ->setLocationId( null )
                ->setUnits( 1 )
                ->setExtras( array() );

            $chain = new BooklyLib\Chain();
            $chain->add( $chain_item );

            // Use the same parameters as the original modern booking form
            $params = array( 'every' => 1, 'full_day' => false );
            if ( BooklyLib\Config::useClientTimeZone() ) {
                $params['time_zone'] = null;
                $params['time_zone_offset'] = null;
            }

            $scheduler = new BooklyLib\Scheduler(
                $chain,
                $start_datetime->format( 'Y-m-d H:i:s' ),
                $start_datetime->format( 'Y-m-d' ),
                'daily',
                $params,
                array(),
                BooklyLib\Config::waitingListActive()
            );

            $schedule = $scheduler->scheduleForFrontend( 1 );

            // Check if there are available time slots for this exact time
            if ( isset( $schedule[0]['options'] ) && count( $schedule[0]['options'] ) > 0 ) {
                foreach ( $schedule[0]['options'] as $option ) {
                    $option_datetime = date_create( $option['datetime'] );
                    if ( $option_datetime && $option_datetime->format( 'Y-m-d H:i' ) === $start_datetime->format( 'Y-m-d H:i' ) ) {
                        return true;
                    }
                }
            }

            return false;

        } catch ( \Exception $e ) {
            if ( defined( 'BOOKLY_CUSTOM_SEARCH_DEBUG' ) && BOOKLY_CUSTOM_SEARCH_DEBUG ) {
                error_log( 'Scheduler availability check failed: ' . $e->getMessage() );
            }
            return false;
        }
    }

    /**
     * Check basic availability (no conflicting appointments)
     *
     * @param int $staff_id
     * @param \DateTime $start_datetime
     * @param int $duration_seconds
     * @return bool
     */
    private static function checkBasicAvailability( $staff_id, $start_datetime, $duration_seconds )
    {
        $end_datetime = clone $start_datetime;
        $end_datetime->modify( '+' . $duration_seconds . ' seconds' );

        // Check for conflicting appointments
        $conflicts = Entities\Appointment::query()
            ->where( 'staff_id', $staff_id )
            ->whereRaw(
                '(start_date < %s AND end_date > %s) OR (start_date < %s AND end_date > %s) OR (start_date >= %s AND start_date < %s)',
                array(
                    $end_datetime->format( 'Y-m-d H:i:s' ),
                    $start_datetime->format( 'Y-m-d H:i:s' ),
                    $start_datetime->format( 'Y-m-d H:i:s' ),
                    $end_datetime->format( 'Y-m-d H:i:s' ),
                    $start_datetime->format( 'Y-m-d H:i:s' ),
                    $end_datetime->format( 'Y-m-d H:i:s' )
                )
            )
            ->count();

        return $conflicts === 0;
    }

    /**
     * Check staff working hours
     *
     * @param int $staff_id
     * @param \DateTime $start_datetime
     * @param int $duration_seconds
     * @return bool
     */
    private static function checkStaffWorkingHours( $staff_id, $start_datetime, $duration_seconds )
    {
        $day_of_week = (int) $start_datetime->format( 'w' ); // 0 = Sunday, 6 = Saturday

        // Get staff schedule for this day
        $schedule = Entities\StaffScheduleItem::query()
            ->where( 'staff_id', $staff_id )
            ->where( 'day_index', $day_of_week + 1 ) // Bookly uses 1-7 instead of 0-6
            ->fetchRow();

        if ( ! $schedule ) {
            return false; // No schedule defined for this day
        }

        $start_time = $start_datetime->format( 'H:i:s' );
        $end_datetime = clone $start_datetime;
        $end_datetime->modify( '+' . $duration_seconds . ' seconds' );
        $end_time = $end_datetime->format( 'H:i:s' );

        // Check if the appointment fits within working hours
        return $start_time >= $schedule['start_time'] && $end_time <= $schedule['end_time'];
    }

    /**
     * Use Bookly's scheduler for detailed availability checking
     *
     * @param int $staff_id
     * @param int $service_id
     * @param \DateTime $start_datetime
     * @param int $duration_seconds
     * @return bool
     */
    private static function checkSchedulerAvailability( $staff_id, $service_id, $start_datetime, $duration_seconds )
    {
        try {
            // Create a chain item for availability checking
            $chain_item = new BooklyLib\ChainItem();
            $chain_item
                ->setStaffIds( array( $staff_id ) )
                ->setServiceId( $service_id )
                ->setNumberOfPersons( 1 )
                ->setQuantity( 1 )
                ->setLocationId( null )
                ->setUnits( 1 )
                ->setExtras( array() );

            $chain = new BooklyLib\Chain();
            $chain->add( $chain_item );

            $params = array( 'every' => 1, 'full_day' => false );
            if ( BooklyLib\Config::useClientTimeZone() ) {
                $params['time_zone'] = null;
                $params['time_zone_offset'] = null;
            }

            $scheduler = new BooklyLib\Scheduler(
                $chain,
                $start_datetime->format( 'Y-m-d H:i:s' ),
                $start_datetime->format( 'Y-m-d' ),
                'daily',
                $params,
                array(),
                false
            );

            $schedule = $scheduler->scheduleForFrontend( 1 );

            // Check if there are available time slots
            if ( isset( $schedule[0]['options'] ) && count( $schedule[0]['options'] ) > 0 ) {
                // Check if our specific time slot is available
                foreach ( $schedule[0]['options'] as $option ) {
                    $option_start = date_create( $option['datetime'] );
                    if ( $option_start && $option_start->format( 'Y-m-d H:i' ) === $start_datetime->format( 'Y-m-d H:i' ) ) {
                        return true;
                    }
                }
            }

            return false;

        } catch ( \Exception $e ) {
            return false;
        }
    }
    
    /**
     * Format duration for display
     * 
     * @param float $hours
     * @return string
     */
    private static function formatDuration( $hours )
    {
        if ( $hours == 1 ) {
            return '1 ' . __( 'hour', 'bookly' );
        } else {
            return $hours . ' ' . __( 'hours', 'bookly' );
        }
    }
    
    /**
     * Generate booking URL to continue with Bookly's standard flow
     *
     * @param int $service_id
     * @param int $staff_id
     * @param string $datetime
     * @return string
     */
    private static function generateBookingUrl( $service_id, $staff_id, $datetime )
    {
        // Generate a URL that will trigger our continue booking handler
        return admin_url( 'admin-ajax.php' ) . '?' . http_build_query( array(
            'action' => 'bookly_custom_continue_booking',
            'service_id' => $service_id,
            'staff_id' => $staff_id,
            'datetime' => urlencode( $datetime ),
            'nonce' => wp_create_nonce( 'bookly_custom_continue_nonce' ),
        ) );
    }

    /**
     * Continue booking with selected service and redirect to Bookly's flow
     */
    public static function continueBooking()
    {
        // Verify nonce
        if ( ! wp_verify_nonce( self::parameter( 'nonce' ), 'bookly_custom_continue_nonce' ) ) {
            wp_die( 'Invalid nonce' );
        }

        $service_id = (int) self::parameter( 'service_id' );
        $staff_id = (int) self::parameter( 'staff_id' );
        $datetime = self::parameter( 'datetime' );

        // Validate parameters
        if ( ! $service_id || ! $staff_id || ! $datetime ) {
            wp_die( 'Invalid parameters' );
        }

        try {
            // Create or get user booking data
            $form_id = 'custom_search_' . uniqid();
            $userData = new BooklyLib\UserBookingData( $form_id );

            // Create chain item with selected service and staff
            $chain_item = new BooklyLib\ChainItem();
            $chain_item
                ->setStaffIds( array( $staff_id ) )
                ->setServiceId( $service_id )
                ->setNumberOfPersons( 1 )
                ->setQuantity( 1 )
                ->setLocationId( null )
                ->setUnits( 1 )
                ->setExtras( array() );

            // Set the specific datetime
            $start_datetime = date_create( $datetime );
            if ( $start_datetime ) {
                $chain_item->setSlots( array( array(
                    'datetime' => $start_datetime->format( 'Y-m-d H:i:s' ),
                    'staff_id' => $staff_id
                ) ) );
            }

            // Add to user data
            $userData->resetChain();
            $userData->chain->add( $chain_item );
            $userData->sessionSave();

            // Redirect to Bookly's booking form at the extras step
            $redirect_url = self::getBooklyFormUrl( $form_id );
            wp_redirect( $redirect_url );
            exit;

        } catch ( \Exception $e ) {
            wp_die( 'Error processing booking: ' . $e->getMessage() );
        }
    }

    /**
     * Get the URL for Bookly's booking form
     *
     * @param string $form_id
     * @return string
     */
    private static function getBooklyFormUrl( $form_id )
    {
        // Try to find a page with Bookly shortcode
        $pages = get_pages();
        foreach ( $pages as $page ) {
            if ( has_shortcode( $page->post_content, 'bookly-form' ) ) {
                return add_query_arg( array(
                    'bookly_form_id' => $form_id,
                    'step' => 'extras'
                ), get_permalink( $page->ID ) );
            }
        }

        // Fallback to current page
        return add_query_arg( array(
            'bookly_form_id' => $form_id,
            'step' => 'extras'
        ), home_url() );
    }

    /**
     * Normalize date format to YYYY-MM-DD
     *
     * @param string $date
     * @return string|false
     */
    private static function normalizeDateFormat( $date )
    {
        // Try different date formats
        $formats = array(
            'Y-m-d',           // 2025-07-10
            'm/d/Y',           // 07/10/2025
            'd/m/Y',           // 10/07/2025
            'Y/m/d',           // 2025/07/10
            'm-d-Y',           // 07-10-2025
            'd-m-Y',           // 10-07-2025
            'F j, Y',          // July 10, 2025
            'M j, Y',          // Jul 10, 2025
            'j F Y',           // 10 July 2025
            'j M Y',           // 10 Jul 2025
            'd.m.Y',           // 10.07.2025
            'Y.m.d',           // 2025.07.10
        );

        foreach ( $formats as $format ) {
            $parsed_date = \DateTime::createFromFormat( $format, $date );
            if ( $parsed_date && $parsed_date->format( $format ) === $date ) {
                return $parsed_date->format( 'Y-m-d' );
            }
        }

        // Try strtotime as fallback
        $timestamp = strtotime( $date );
        if ( $timestamp !== false ) {
            return date( 'Y-m-d', $timestamp );
        }

        return false;
    }
}
