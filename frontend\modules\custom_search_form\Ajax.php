<?php
namespace BooklyPro\Frontend\Modules\CustomSearchForm;

use Bookly\Lib as BooklyLib;
use Bookly\Lib\Entities;

/**
 * Ajax handlers for Custom Search Form
 */
class Ajax extends BooklyLib\Base\Ajax
{
    /**
     * Debug counters
     */
    private static $debug_services_checked = 0;
    private static $debug_staff_checked = 0;

    /**
     * @inheritDoc
     */
    protected static function permissions()
    {
        return array( '_default' => 'anonymous' );
    }
    
    /**
     * Search for available services based on date, time, and duration
     */
    public static function searchAvailability()
    {
        // Add error logging for debugging
        error_log( '[BOOKLY CUSTOM SEARCH] searchAvailability() called' );

        // Verify nonce
        if ( ! wp_verify_nonce( self::parameter( 'nonce' ), 'bookly_custom_search_nonce' ) ) {
            error_log( '[BOOKLY CUSTOM SEARCH] Invalid nonce in searchAvailability' );
            wp_send_json_error( array( 'message' => 'Invalid nonce' ) );
            return;
        }

        $date = self::parameter( 'date' );
        $time = self::parameter( 'time' );
        $duration = (int) self::parameter( 'duration' );

        // Debug logging
        if ( defined( 'BOOKLY_CUSTOM_SEARCH_DEBUG' ) && BOOKLY_CUSTOM_SEARCH_DEBUG ) {
            error_log( sprintf( 'Custom Search Request - Date: %s, Time: %s, Duration: %d', $date, $time, $duration ) );
        }

        // Validate parameters
        if ( empty( $date ) || empty( $time ) || $duration <= 0 ) {
            wp_send_json_error( array( 'message' => 'Please fill in all required fields.' ) );
            return;
        }

        // Convert and validate date format
        $normalized_date = self::normalizeDateFormat( $date );
        if ( ! $normalized_date ) {
            wp_send_json_error( array( 'message' => 'Invalid date format. Please select a valid date.' ) );
            return;
        }
        $date = $normalized_date;

        // Validate time format
        if ( ! preg_match( '/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/', $time ) ) {
            wp_send_json_error( array( 'message' => 'Invalid time format.' ) );
            return;
        }

        // Validate duration range
        if ( $duration < 1 || $duration > 24 ) {
            wp_send_json_error( array( 'message' => 'Duration must be between 1 and 24 hours.' ) );
            return;
        }

        try {
            // Create datetime string
            $datetime = $date . ' ' . $time;
            $start_datetime = date_create( $datetime );

            if ( ! $start_datetime ) {
                wp_send_json_error( array( 'message' => 'Invalid date/time combination.' ) );
                return;
            }

            // Check if date is not in the past
            $now = new \DateTime();
            if ( $start_datetime < $now ) {
                wp_send_json_error( array( 'message' => 'Cannot book appointments in the past.' ) );
                return;
            }

            // Check if date is within booking limits
            $max_days = BooklyLib\Config::getMaximumAvailableDaysForBooking();
            $max_date = clone $now;
            $max_date->modify( '+' . $max_days . ' days' );

            if ( $start_datetime > $max_date ) {
                wp_send_json_error( array( 'message' => sprintf( 'Cannot book more than %d days in advance.', $max_days ) ) );
                return;
            }

            // Get available services
            $available_services = self::getAvailableServices( $start_datetime, $duration );

            // Log search for debugging (if debug mode is enabled)
            if ( defined( 'BOOKLY_CUSTOM_SEARCH_DEBUG' ) && BOOKLY_CUSTOM_SEARCH_DEBUG ) {
                error_log( sprintf(
                    'Custom Search: %s %s - Found %d services (checked %d services, %d staff)',
                    $date, $time, count( $available_services ), self::$debug_services_checked, self::$debug_staff_checked
                ) );
            }

            // Always include debug info for troubleshooting
            $debug_info = array(
                'total_combinations_checked' => self::$debug_services_checked ?? 0,
                'total_staff_checked' => self::$debug_staff_checked ?? 0,
                'search_datetime' => $start_datetime->format( 'Y-m-d H:i:s' ),
                'debug_enabled' => defined( 'BOOKLY_CUSTOM_SEARCH_DEBUG' ) && BOOKLY_CUSTOM_SEARCH_DEBUG,
            );

            wp_send_json_success( array(
                'services' => $available_services,
                'search_params' => array(
                    'date' => $date,
                    'time' => $time,
                    'duration' => $duration,
                    'datetime' => $start_datetime->format( 'Y-m-d H:i:s' )
                ),
                'debug' => $debug_info,
                'message' => count( $available_services ) === 0 ?
                    sprintf( 'No services found. Checked %d combinations for %s at %s',
                        self::$debug_services_checked, $date, $time ) :
                    sprintf( 'Found %d services from %d combinations checked',
                        count( $available_services ), self::$debug_services_checked )
            ) );

        } catch ( \Exception $e ) {
            // Log error for debugging
            error_log( '[BOOKLY CUSTOM SEARCH] Error in searchAvailability: ' . $e->getMessage() );
            error_log( '[BOOKLY CUSTOM SEARCH] Stack trace: ' . $e->getTraceAsString() );

            wp_send_json_error( array(
                'message' => 'An error occurred while searching. Please try again.',
                'error_details' => $e->getMessage()
            ) );
        }
    }
    
    /**
     * Get available services for the given datetime
     *
     * Uses the EXACT same logic as the original Bookly booking form:
     * 1. Get all service-staff combinations
     * 2. For each combination, use Bookly's scheduler to get available time slots for the day
     * 3. Check if the user's requested time matches any available slot
     *
     * @param \DateTime $start_datetime The exact date and time the user selected
     * @param int $duration_hours User's preferred duration (for display only)
     * @return array Available services at the requested time
     */
    private static function getAvailableServices( $start_datetime, $duration_hours )
    {
        $available_services = array();

        // Reset debug counters
        self::$debug_services_checked = 0;
        self::$debug_staff_checked = 0;

        $date = $start_datetime->format( 'Y-m-d' );
        $requested_time = $start_datetime->format( 'H:i' );

        // Get all service-staff combinations (same as original Bookly)
        $staff_services = Entities\StaffService::query( 'ss' )
            ->select( 'ss.staff_id, ss.service_id, ss.price, s.full_name as staff_name, srv.title as service_title, srv.duration as service_duration' )
            ->leftJoin( 'Staff', 's', 's.id = ss.staff_id' )
            ->leftJoin( 'Service', 'srv', 'srv.id = ss.service_id' )
            ->where( 's.visibility', 'public' )
            ->where( 'srv.visibility', 'public' )
            ->fetchArray();

        // Always log this for debugging
        error_log( sprintf( '[BOOKLY CUSTOM SEARCH] Found %d staff-service combinations to check for %s at %s',
            count( $staff_services ), $date, $requested_time ) );

        if ( empty( $staff_services ) ) {
            // Diagnostic queries to understand what's missing
            $services_count = Entities\Service::query()->where( 'visibility', 'public' )->count();
            $staff_count = Entities\Staff::query()->where( 'visibility', 'public' )->count();
            $staff_service_count = Entities\StaffService::query()->count();

            error_log( sprintf( '[BOOKLY CUSTOM SEARCH] DIAGNOSTIC: Services: %d, Staff: %d, Staff-Service links: %d',
                $services_count, $staff_count, $staff_service_count ) );

            // Get actual service data
            $services = Entities\Service::query()->select( 'id, title, visibility' )->fetchArray();
            error_log( '[BOOKLY CUSTOM SEARCH] SERVICES: ' . print_r( $services, true ) );

            // Get actual staff data
            $staff = Entities\Staff::query()->select( 'id, full_name, visibility' )->fetchArray();
            error_log( '[BOOKLY CUSTOM SEARCH] STAFF: ' . print_r( $staff, true ) );

            // Get staff-service links
            $staff_services_all = Entities\StaffService::query()->select( 'staff_id, service_id' )->fetchArray();
            error_log( '[BOOKLY CUSTOM SEARCH] STAFF-SERVICE LINKS: ' . print_r( $staff_services_all, true ) );

            // Check if there are any services at all
            if ( $services_count === 0 ) {
                error_log( '[BOOKLY CUSTOM SEARCH] ERROR: No services found in database! Please create services in Bookly admin.' );
            }

            // Check if there are any staff at all
            if ( $staff_count === 0 ) {
                error_log( '[BOOKLY CUSTOM SEARCH] ERROR: No staff found in database! Please create staff members in Bookly admin.' );
            }

            // Check if services are linked to staff
            if ( $services_count > 0 && $staff_count > 0 && $staff_service_count === 0 ) {
                error_log( '[BOOKLY CUSTOM SEARCH] ERROR: Services and staff exist but are not linked! Please assign staff to services in Bookly admin.' );
            }

            return $available_services;
        }

        // For each combination, use Bookly's scheduler to get available slots
        foreach ( $staff_services as $staff_service ) {
            self::$debug_services_checked++;
            self::$debug_staff_checked++;

            $service_id = $staff_service['service_id'];
            $staff_id = $staff_service['staff_id'];
            $service_duration_seconds = $staff_service['service_duration'];

            // Convert requested duration to seconds for comparison
            $requested_duration_seconds = $duration_hours * 3600;

            // Only check services that match the requested duration (±15 min tolerance)
            $duration_tolerance = 15 * 60; // 15 minutes in seconds
            if ( abs( $service_duration_seconds - $requested_duration_seconds ) > $duration_tolerance ) {
                continue; // Skip this service as it doesn't match the requested duration
            }

            // Create chain item exactly like original Bookly form
            $chain_item = new BooklyLib\ChainItem();
            $chain_item
                ->setStaffIds( array( $staff_id ) )
                ->setServiceId( $service_id )
                ->setNumberOfPersons( 1 )
                ->setQuantity( 1 )
                ->setLocationId( null )
                ->setUnits( 1 )
                ->setExtras( array() );

            $chain = new BooklyLib\Chain();
            $chain->add( $chain_item );

            // Use EXACT same parameters as original form
            $params = array( 'every' => 1, 'full_day' => false );
            if ( BooklyLib\Config::useClientTimeZone() ) {
                $params['time_zone'] = null;
                $params['time_zone_offset'] = null;
            }

            $scheduler = new BooklyLib\Scheduler(
                $chain,
                $date . ' 00:00:00',  // Start from beginning of day
                $date,
                'daily',
                $params,
                array(),
                BooklyLib\Config::waitingListActive()
            );

            $schedule = $scheduler->scheduleForFrontend( 1 );

            // Debug: Log scheduler results
            $options_count = isset( $schedule[0]['options'] ) ? count( $schedule[0]['options'] ) : 0;
            error_log( sprintf( '[BOOKLY CUSTOM SEARCH] Staff %d, Service %d (%s): %d time slots available',
                $staff_id, $service_id, $staff_service['service_title'], $options_count ) );

            // Check if our requested time is in the available slots
            if ( isset( $schedule[0]['options'] ) && count( $schedule[0]['options'] ) > 0 ) {
                // Debug: Log the structure of options to understand the data format
                error_log( '[BOOKLY CUSTOM SEARCH] Option structure: ' . print_r( $schedule[0]['options'][0], true ) );

                foreach ( $schedule[0]['options'] as $option ) {
                    // The actual time is in the 'title' field, not the datetime value
                    // Convert the title (e.g., "11:00 am") to 24-hour format for comparison
                    if ( isset( $option['title'] ) ) {
                        $option_time = $option['title'];

                        // Convert 12-hour format to 24-hour format
                        $option_datetime = date_create_from_format( 'g:i a', $option_time );
                        if ( $option_datetime && $option_datetime->format( 'H:i' ) === $requested_time ) {
                            // Found a match! Add to results
                            $end_datetime = clone $start_datetime;
                            $end_datetime->modify( '+' . $service_duration_seconds . ' seconds' );

                            $available_services[] = array(
                                'service_id' => $service_id,
                                'service_title' => $staff_service['service_title'],
                                'staff_id' => $staff_id,
                                'staff_name' => $staff_service['staff_name'],
                                'price' => BooklyLib\Utils\Price::format( $staff_service['price'] ),
                                'duration' => round( $service_duration_seconds / 3600, 1 ),
                                'duration_formatted' => self::formatDuration( $service_duration_seconds / 3600 ),
                                'start_time' => $start_datetime->format( 'H:i' ),
                                'end_time' => $end_datetime->format( 'H:i' ),
                                'booking_url' => self::generateBookingUrl( $service_id, $staff_id, $start_datetime->format( 'Y-m-d H:i:s' ) )
                            );

                            if ( defined( 'BOOKLY_CUSTOM_SEARCH_DEBUG' ) && BOOKLY_CUSTOM_SEARCH_DEBUG ) {
                                error_log( sprintf( 'Found available: %s with %s at %s', $staff_service['service_title'], $staff_service['staff_name'], $requested_time ) );
                            }

                            break; // Found the time slot, no need to check other options for this combination
                        }
                    }
                }
            }
        }

        return $available_services;
    }
    
    /**
     * Check if staff member is available at the given time
     *
     * @param int $staff_id
     * @param int $service_id
     * @param \DateTime $start_datetime
     * @param int $duration_seconds
     * @return bool
     */
    private static function isStaffAvailable( $staff_id, $service_id, $start_datetime, $duration_seconds )
    {
        try {
            // First check basic conflicts with existing appointments
            if ( ! self::checkBasicAvailability( $staff_id, $start_datetime, $duration_seconds ) ) {
                return false;
            }

            // Check staff working hours
            if ( ! self::checkStaffWorkingHours( $staff_id, $start_datetime, $duration_seconds ) ) {
                return false;
            }

            // Use Bookly's scheduler for detailed availability checking
            return self::checkSchedulerAvailability( $staff_id, $service_id, $start_datetime, $duration_seconds );

        } catch ( \Exception $e ) {
            return false;
        }
    }

    /**
     * Check basic availability (no conflicting appointments)
     *
     * @param int $staff_id
     * @param \DateTime $start_datetime
     * @param int $duration_seconds
     * @return bool
     */
    private static function checkBasicAvailability( $staff_id, $start_datetime, $duration_seconds )
    {
        $end_datetime = clone $start_datetime;
        $end_datetime->modify( '+' . $duration_seconds . ' seconds' );

        // Check for conflicting appointments
        $conflicts = Entities\Appointment::query()
            ->where( 'staff_id', $staff_id )
            ->whereRaw(
                '(start_date < %s AND end_date > %s) OR (start_date < %s AND end_date > %s) OR (start_date >= %s AND start_date < %s)',
                array(
                    $end_datetime->format( 'Y-m-d H:i:s' ),
                    $start_datetime->format( 'Y-m-d H:i:s' ),
                    $start_datetime->format( 'Y-m-d H:i:s' ),
                    $end_datetime->format( 'Y-m-d H:i:s' ),
                    $start_datetime->format( 'Y-m-d H:i:s' ),
                    $end_datetime->format( 'Y-m-d H:i:s' )
                )
            )
            ->count();

        return $conflicts === 0;
    }

    /**
     * Check staff working hours
     *
     * @param int $staff_id
     * @param \DateTime $start_datetime
     * @param int $duration_seconds
     * @return bool
     */
    private static function checkStaffWorkingHours( $staff_id, $start_datetime, $duration_seconds )
    {
        $day_of_week = (int) $start_datetime->format( 'w' ); // 0 = Sunday, 6 = Saturday

        // Get staff schedule for this day
        $schedule = Entities\StaffScheduleItem::query()
            ->where( 'staff_id', $staff_id )
            ->where( 'day_index', $day_of_week + 1 ) // Bookly uses 1-7 instead of 0-6
            ->fetchRow();

        if ( ! $schedule ) {
            return false; // No schedule defined for this day
        }

        $start_time = $start_datetime->format( 'H:i:s' );
        $end_datetime = clone $start_datetime;
        $end_datetime->modify( '+' . $duration_seconds . ' seconds' );
        $end_time = $end_datetime->format( 'H:i:s' );

        // Check if the appointment fits within working hours
        return $start_time >= $schedule['start_time'] && $end_time <= $schedule['end_time'];
    }

    /**
     * Use Bookly's scheduler for detailed availability checking
     *
     * @param int $staff_id
     * @param int $service_id
     * @param \DateTime $start_datetime
     * @param int $duration_seconds
     * @return bool
     */
    private static function checkSchedulerAvailability( $staff_id, $service_id, $start_datetime, $duration_seconds )
    {
        try {
            // Create a chain item for availability checking
            $chain_item = new BooklyLib\ChainItem();
            $chain_item
                ->setStaffIds( array( $staff_id ) )
                ->setServiceId( $service_id )
                ->setNumberOfPersons( 1 )
                ->setQuantity( 1 )
                ->setLocationId( null )
                ->setUnits( 1 )
                ->setExtras( array() );

            $chain = new BooklyLib\Chain();
            $chain->add( $chain_item );

            $params = array( 'every' => 1, 'full_day' => false );
            if ( BooklyLib\Config::useClientTimeZone() ) {
                $params['time_zone'] = null;
                $params['time_zone_offset'] = null;
            }

            $scheduler = new BooklyLib\Scheduler(
                $chain,
                $start_datetime->format( 'Y-m-d H:i:s' ),
                $start_datetime->format( 'Y-m-d' ),
                'daily',
                $params,
                array(),
                false
            );

            $schedule = $scheduler->scheduleForFrontend( 1 );

            // Debug logging
            if ( defined( 'BOOKLY_CUSTOM_SEARCH_DEBUG' ) && BOOKLY_CUSTOM_SEARCH_DEBUG ) {
                $options_count = isset( $schedule[0]['options'] ) ? count( $schedule[0]['options'] ) : 0;
                error_log( sprintf(
                    'Scheduler check for staff %d, service %d at %s: %d options available',
                    $staff_id, $service_id, $start_datetime->format( 'Y-m-d H:i' ), $options_count
                ) );
            }

            // Check if there are available time slots
            if ( isset( $schedule[0]['options'] ) && count( $schedule[0]['options'] ) > 0 ) {
                // Check if our specific time slot is available
                foreach ( $schedule[0]['options'] as $option ) {
                    $option_start = date_create( $option['datetime'] );
                    if ( $option_start && $option_start->format( 'Y-m-d H:i' ) === $start_datetime->format( 'Y-m-d H:i' ) ) {
                        if ( defined( 'BOOKLY_CUSTOM_SEARCH_DEBUG' ) && BOOKLY_CUSTOM_SEARCH_DEBUG ) {
                            error_log( sprintf( 'Found exact match for %s', $start_datetime->format( 'Y-m-d H:i' ) ) );
                        }
                        return true;
                    }
                }
            }

            return false;

        } catch ( \Exception $e ) {
            return false;
        }
    }
    
    /**
     * Format duration for display
     * 
     * @param float $hours
     * @return string
     */
    private static function formatDuration( $hours )
    {
        if ( $hours == 1 ) {
            return '1 ' . __( 'hour', 'bookly' );
        } else {
            return $hours . ' ' . __( 'hours', 'bookly' );
        }
    }
    
    /**
     * Generate booking URL to continue with Bookly's standard flow
     *
     * @param int $service_id
     * @param int $staff_id
     * @param string $datetime
     * @return string
     */
    private static function generateBookingUrl( $service_id, $staff_id, $datetime )
    {
        // Generate a URL that will trigger our continue booking handler
        $url = admin_url( 'admin-ajax.php' ) . '?' . http_build_query( array(
            'action' => 'bookly_custom_continue_booking',
            'service_id' => $service_id,
            'staff_id' => $staff_id,
            'datetime' => urlencode( $datetime ),
            'nonce' => wp_create_nonce( 'bookly_custom_continue_nonce' ),
        ) );

        // Debug logging
        if ( defined( 'BOOKLY_CUSTOM_SEARCH_DEBUG' ) && BOOKLY_CUSTOM_SEARCH_DEBUG ) {
            error_log( sprintf( '[BOOKLY CUSTOM SEARCH] Generated booking URL: %s', $url ) );
        }

        return $url;
    }

    /**
     * Continue booking with selected service and redirect to Bookly's flow
     */
    public static function continueBooking()
    {
        // Debug logging
        error_log( '[BOOKLY CUSTOM SEARCH] continueBooking() called' );

        // Verify nonce
        if ( ! wp_verify_nonce( self::parameter( 'nonce' ), 'bookly_custom_continue_nonce' ) ) {
            error_log( '[BOOKLY CUSTOM SEARCH] Invalid nonce in continueBooking' );
            wp_die( 'Invalid nonce' );
        }

        $service_id = (int) self::parameter( 'service_id' );
        $staff_id = (int) self::parameter( 'staff_id' );
        $datetime = urldecode( self::parameter( 'datetime' ) );

        // Validate parameters
        if ( ! $service_id || ! $staff_id || ! $datetime ) {
            wp_die( 'Invalid parameters' );
        }

        // Debug logging
        if ( defined( 'BOOKLY_CUSTOM_SEARCH_DEBUG' ) && BOOKLY_CUSTOM_SEARCH_DEBUG ) {
            error_log( sprintf( '[BOOKLY CUSTOM SEARCH] Continue booking: Service %d, Staff %d, DateTime %s', $service_id, $staff_id, $datetime ) );
        }

        // Simple approach: redirect to current page with booking parameters
        // This will show a booking summary and allow user to proceed with standard Bookly form

        $redirect_url = add_query_arg( array(
            'bookly_booking' => '1',
            'service_id' => $service_id,
            'staff_id' => $staff_id,
            'datetime' => urlencode( $datetime ),
            'booking_token' => wp_create_nonce( 'bookly_booking_' . $service_id . '_' . $staff_id . '_' . $datetime )
        ), wp_get_referer() ?: home_url() );

        if ( defined( 'BOOKLY_CUSTOM_SEARCH_DEBUG' ) && BOOKLY_CUSTOM_SEARCH_DEBUG ) {
            error_log( sprintf( '[BOOKLY CUSTOM SEARCH] Redirecting to simple booking: %s', $redirect_url ) );
        }

        wp_redirect( $redirect_url );
        exit;
    }

    /**
     * Complete booking with customer details
     */
    public static function completeBooking()
    {
        // Add error logging for debugging
        error_log( '[BOOKLY CUSTOM SEARCH] completeBooking() called' );

        try {
    {
        // Verify nonce
        if ( ! wp_verify_nonce( self::parameter( 'nonce' ), 'bookly_custom_complete_nonce' ) ) {
            wp_send_json_error( array( 'message' => 'Invalid nonce' ) );
            return;
        }

        $form_id = self::parameter( 'form_id' );
        $service_id = (int) self::parameter( 'service_id' );
        $staff_id = (int) self::parameter( 'staff_id' );
        $datetime = self::parameter( 'datetime' );
        $customer_name = sanitize_text_field( self::parameter( 'customer_name' ) );
        $customer_email = sanitize_email( self::parameter( 'customer_email' ) );
        $customer_phone = sanitize_text_field( self::parameter( 'customer_phone' ) );
        $customer_notes = sanitize_textarea_field( self::parameter( 'customer_notes' ) );

        // Validate required fields
        if ( empty( $customer_name ) || empty( $customer_email ) ) {
            wp_send_json_error( array( 'message' => 'Name and email are required' ) );
            return;
        }

            // Get or create customer
            $customer = BooklyLib\Entities\Customer::query()
                ->where( 'email', $customer_email )
                ->findOne();

            if ( ! $customer ) {
                $customer = new BooklyLib\Entities\Customer();
                $customer->setEmail( $customer_email );
            }

            // Set customer details
            $customer->setFullName( $customer_name );
            $customer->setPhone( $customer_phone );
            $customer->setNotes( $customer_notes );
            $customer->save();

            // Get service and staff
            $service = BooklyLib\Entities\Service::find( $service_id );
            $staff = BooklyLib\Entities\Staff::find( $staff_id );

            if ( ! $service || ! $staff ) {
                wp_send_json_error( array( 'message' => 'Invalid service or staff' ) );
                return;
            }

            // Create appointment
            $appointment = new BooklyLib\Entities\Appointment();
            $appointment
                ->setServiceId( $service_id )
                ->setStaffId( $staff_id )
                ->setStartDate( $datetime )
                ->setEndDate( date( 'Y-m-d H:i:s', strtotime( $datetime ) + $service->getDuration() ) )
                ->setInternalNote( $customer_notes )
                ->save();

            // Create customer appointment
            $customer_appointment = new BooklyLib\Entities\CustomerAppointment();
            $customer_appointment
                ->setCustomerId( $customer->getId() )
                ->setAppointmentId( $appointment->getId() )
                ->setNumberOfPersons( 1 )
                ->setStatus( BooklyLib\Entities\CustomerAppointment::STATUS_APPROVED )
                ->save();

            // Send notifications (if enabled)
            if ( class_exists( 'BooklyPro\Lib\Notifications\Cart\Sender' ) ) {
                // Create order for notifications
                $order = new BooklyLib\DataHolders\Booking\Order();
                $order->setCustomer( $customer );

                $item = new BooklyLib\DataHolders\Booking\Item();
                $item->setCA( $customer_appointment );
                $order->addItem( $item );

                \BooklyPro\Lib\Notifications\Cart\Sender::sendCombined( $order );
            }

            wp_send_json_success( array(
                'message' => 'Booking completed successfully',
                'appointment_id' => $appointment->getId()
            ) );

        } catch ( Exception $e ) {
            error_log( '[BOOKLY CUSTOM SEARCH] Error in completeBooking: ' . $e->getMessage() );
            wp_send_json_error( array( 'message' => 'Failed to create booking: ' . $e->getMessage() ) );
        }
    }
    }

    /**
     * Get the URL for Bookly's booking form
     *
     * @param string $form_id
     * @return string
     */
    private static function getBooklyFormUrl( $form_id )
    {
        // First, try to find a page with the [bookly-form] shortcode
        $pages = get_pages( array( 'post_status' => 'publish' ) );
        foreach ( $pages as $page ) {
            if ( has_shortcode( $page->post_content, 'bookly-form' ) ) {
                if ( defined( 'BOOKLY_CUSTOM_SEARCH_DEBUG' ) && BOOKLY_CUSTOM_SEARCH_DEBUG ) {
                    error_log( sprintf( '[BOOKLY CUSTOM SEARCH] Found bookly-form on page: %s', $page->post_title ) );
                }
                return add_query_arg( array(
                    'bookly_form_id' => $form_id,
                    'step' => 'extras'
                ), get_permalink( $page->ID ) );
            }
        }

        // Try to find any post with bookly-form shortcode
        $posts = get_posts( array(
            'post_type' => array( 'page', 'post' ),
            'post_status' => 'publish',
            'numberposts' => -1,
            'meta_query' => array(
                array(
                    'key' => '_wp_page_template',
                    'compare' => 'EXISTS'
                )
            )
        ) );

        foreach ( $posts as $post ) {
            if ( has_shortcode( $post->post_content, 'bookly-form' ) ) {
                if ( defined( 'BOOKLY_CUSTOM_SEARCH_DEBUG' ) && BOOKLY_CUSTOM_SEARCH_DEBUG ) {
                    error_log( sprintf( '[BOOKLY CUSTOM SEARCH] Found bookly-form on post: %s', $post->post_title ) );
                }
                return add_query_arg( array(
                    'bookly_form_id' => $form_id,
                    'step' => 'extras'
                ), get_permalink( $post->ID ) );
            }
        }

        // If no Bookly form page found, redirect to the current page with the form embedded
        // This assumes the current page can handle the booking form
        $current_url = wp_get_referer() ?: home_url();

        if ( defined( 'BOOKLY_CUSTOM_SEARCH_DEBUG' ) && BOOKLY_CUSTOM_SEARCH_DEBUG ) {
            error_log( '[BOOKLY CUSTOM SEARCH] No bookly-form page found, using current page' );
        }

        return add_query_arg( array(
            'bookly_form_id' => $form_id,
            'step' => 'extras',
            'bookly_continue' => '1'
        ), $current_url );
    }

    /**
     * Normalize date format to YYYY-MM-DD
     *
     * @param string $date
     * @return string|false
     */
    private static function normalizeDateFormat( $date )
    {
        // Try different date formats
        $formats = array(
            'Y-m-d',           // 2025-07-10
            'm/d/Y',           // 07/10/2025
            'd/m/Y',           // 10/07/2025
            'Y/m/d',           // 2025/07/10
            'm-d-Y',           // 07-10-2025
            'd-m-Y',           // 10-07-2025
            'F j, Y',          // July 10, 2025
            'M j, Y',          // Jul 10, 2025
            'j F Y',           // 10 July 2025
            'j M Y',           // 10 Jul 2025
            'd.m.Y',           // 10.07.2025
            'Y.m.d',           // 2025.07.10
        );

        foreach ( $formats as $format ) {
            $parsed_date = \DateTime::createFromFormat( $format, $date );
            if ( $parsed_date && $parsed_date->format( $format ) === $date ) {
                return $parsed_date->format( 'Y-m-d' );
            }
        }

        // Try strtotime as fallback
        $timestamp = strtotime( $date );
        if ( $timestamp !== false ) {
            return date( 'Y-m-d', $timestamp );
        }

        return false;
    }
}
