/**
 * Custom Search Form JavaScript
 */
(function($) {
    'use strict';
    
    // Initialize when document is ready
    $(document).ready(function() {
        initCustomSearchForm();
    });
    
    function initCustomSearchForm() {
        $('.bookly-custom-search-form').each(function() {
            var $form = $(this);

            // Initialize date picker
            initDatePicker($form);
            
            // Bind form submission
            bindFormSubmission($form);
            
            // Bind result interactions
            bindResultInteractions($form);
        });
    }
    
    function initDatePicker($form) {
        var $datePicker = $form.find('.bookly-date-picker');

        if ($datePicker.length) {
            // Set default date to today
            var today = new Date();
            $datePicker.val($.datepicker.formatDate(BooklyCustomSearchForm.dateFormat, today));

            $datePicker.datepicker({
                dateFormat: BooklyCustomSearchForm.dateFormat,
                minDate: 0, // Today
                maxDate: BooklyCustomSearchForm.maxDaysForBooking - 1,
                showOtherMonths: true,
                selectOtherMonths: true,
                dayNamesMin: ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'],
                firstDay: 1, // Monday
                changeMonth: true,
                changeYear: true,
                showButtonPanel: true,
                beforeShowDay: function() {
                    // You can add custom logic here to disable specific dates
                    return [true, ''];
                },
                onSelect: function() {
                    // Clear previous results when date changes
                    hideAllStates($form);
                }
            });
        }
    }
    
    function bindFormSubmission($form) {
        $form.find('.bookly-search-form').on('submit', function(e) {
            e.preventDefault();

            var $searchForm = $(this);
            var formData = getFormData($searchForm);

            // Validate form data
            if (!validateFormData(formData, $form)) {
                return;
            }

            // Perform search
            performSearch(formData, $form);
        });

        // Also bind change events to clear results when form changes
        $form.find('select, input').on('change', function() {
            hideAllStates($form);
        });
    }
    
    function getFormData($searchForm) {
        return {
            date: $searchForm.find('[name="search_date"]').val(),
            time: $searchForm.find('[name="search_time"]').val(),
            duration: $searchForm.find('[name="search_duration"]').val()
        };
    }
    
    function validateFormData(formData, $form) {
        var errors = [];
        
        if (!formData.date) {
            errors.push(BooklyCustomSearchForm.l10n.selectDate);
        }
        
        if (!formData.time) {
            errors.push(BooklyCustomSearchForm.l10n.selectTime);
        }
        
        if (!formData.duration) {
            errors.push(BooklyCustomSearchForm.l10n.selectDuration);
        }
        
        if (errors.length > 0) {
            showError($form, errors.join('<br>'));
            return false;
        }
        
        return true;
    }
    
    function performSearch(formData, $form) {
        // Show loading state
        showLoading($form);
        
        // Prepare AJAX data
        var ajaxData = {
            action: 'bookly_custom_search_availability',
            nonce: BooklyCustomSearchForm.nonce,
            date: formData.date,
            time: formData.time,
            duration: formData.duration
        };
        
        // Perform AJAX request
        $.ajax({
            url: BooklyCustomSearchForm.ajaxUrl,
            type: 'POST',
            data: ajaxData,
            dataType: 'json',
            success: function(response) {
                hideLoading($form);
                
                if (response.success) {
                    displayResults(response.data, $form);
                } else {
                    showError($form, response.data.message || 'Search failed');
                }
            },
            error: function() {
                hideLoading($form);
                showError($form, 'Network error occurred. Please try again.');
            }
        });
    }
    
    function displayResults(data, $form) {
        var services = data.services || [];
        var $resultsContainer = $form.find('.bookly-search-results');
        var $resultsGrid = $resultsContainer.find('.bookly-results-grid');

        // Store search parameters for later use
        $form.data('search-params', data.search_params);

        // Hide other states
        hideAllStates($form);

        if (services.length === 0) {
            showNoResults($form);
            return;
        }

        // Clear previous results
        $resultsGrid.empty();
        
        // Generate service cards
        var template = $('#bookly-service-card-template').html();
        
        $.each(services, function(_, service) {
            var cardHtml = template;
            
            // Replace template variables
            cardHtml = cardHtml.replace(/\{\{service_id\}\}/g, service.service_id);
            cardHtml = cardHtml.replace(/\{\{staff_id\}\}/g, service.staff_id);
            cardHtml = cardHtml.replace(/\{\{staff_name\}\}/g, escapeHtml(service.staff_name));
            cardHtml = cardHtml.replace(/\{\{service_title\}\}/g, escapeHtml(service.service_title));
            cardHtml = cardHtml.replace(/\{\{duration_formatted\}\}/g, escapeHtml(service.duration_formatted));
            cardHtml = cardHtml.replace(/\{\{start_time\}\}/g, service.start_time);
            cardHtml = cardHtml.replace(/\{\{end_time\}\}/g, service.end_time);
            cardHtml = cardHtml.replace(/\{\{price\}\}/g, service.price);
            cardHtml = cardHtml.replace(/\{\{booking_url\}\}/g, service.booking_url);
            
            $resultsGrid.append(cardHtml);
        });
        
        // Show results
        $resultsContainer.show();
        
        // Scroll to results
        $('html, body').animate({
            scrollTop: $resultsContainer.offset().top - 50
        }, 500);
    }
    
    function bindResultInteractions($form) {
        // Handle service selection
        $form.on('click', '.bookly-select-service-button', function(e) {
            e.preventDefault();

            var $button = $(this);
            var bookingUrl = $button.data('booking-url');

            // Show loading state on button
            $button.prop('disabled', true);
            $button.html('<i class="bookly-icon bookly-icon-spinner"></i> ' + BooklyCustomSearchForm.l10n.loading);

            if (bookingUrl) {
                // Redirect to continue booking
                window.location.href = bookingUrl;
            } else {
                // Fallback: try to continue with AJAX
                continueBookingAjax($button, $form);
            }
        });

        // Handle search again button
        $form.on('click', '.bookly-search-again-button, .bookly-try-again-button', function(e) {
            e.preventDefault();
            resetForm($form);
        });
    }

    function continueBookingAjax($button, $form) {
        var serviceId = $button.data('service-id');
        var staffId = $button.data('staff-id');
        var searchParams = $form.data('search-params');

        if (!serviceId || !staffId || !searchParams) {
            showError($form, 'Missing booking information');
            return;
        }

        $.ajax({
            url: BooklyCustomSearchForm.ajaxUrl,
            type: 'POST',
            data: {
                action: 'bookly_custom_continue_booking',
                service_id: serviceId,
                staff_id: staffId,
                datetime: searchParams.datetime,
                nonce: BooklyCustomSearchForm.nonce
            },
            success: function(response) {
                if (response.success && response.data.redirect_url) {
                    window.location.href = response.data.redirect_url;
                } else {
                    showError($form, 'Failed to continue booking');
                }
            },
            error: function() {
                showError($form, 'Network error occurred');
            }
        });
    }
    
    function showLoading($form) {
        hideAllStates($form);
        $form.find('.bookly-search-loading').show();
        $form.find('.bookly-search-button-text').hide();
        $form.find('.bookly-search-button-loading').show();
        $form.find('.bookly-search-button').prop('disabled', true);
    }
    
    function hideLoading($form) {
        $form.find('.bookly-search-loading').hide();
        $form.find('.bookly-search-button-text').show();
        $form.find('.bookly-search-button-loading').hide();
        $form.find('.bookly-search-button').prop('disabled', false);
    }
    
    function showNoResults($form) {
        hideAllStates($form);
        $form.find('.bookly-no-results').show();
    }
    
    function showError($form, message) {
        hideAllStates($form);
        $form.find('.bookly-error-message').html(message);
        $form.find('.bookly-search-error').show();
    }
    
    function hideAllStates($form) {
        $form.find('.bookly-search-loading, .bookly-search-results, .bookly-no-results, .bookly-search-error').hide();
    }
    
    function resetForm($form) {
        hideAllStates($form);
        $form.find('.bookly-search-form')[0].reset();
    }
    
    function escapeHtml(text) {
        var map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        
        return text.replace(/[&<>"']/g, function(m) { return map[m]; });
    }
    

    
})(jQuery);
