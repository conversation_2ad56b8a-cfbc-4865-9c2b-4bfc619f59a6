<?php
namespace BooklyPro\Frontend\Modules\CustomSearchForm;

use <PERSON><PERSON>\Lib as BooklyLib;

/**
 * Custom Search Form Module
 * 
 * Provides a custom booking search flow:
 * Date + Time + Duration → Available Services → Extras → Details → Payment
 */
class Module
{
    /**
     * Initialize the module
     */
    public static function init()
    {
        // Register shortcode
        add_shortcode( 'bookly-custom-search-form', array( __CLASS__, 'renderShortcode' ) );
        
        // Register AJAX handlers
        add_action( 'wp_ajax_bookly_custom_search_availability', array( 'BooklyPro\Frontend\Modules\CustomSearchForm\Ajax', 'searchAvailability' ) );
        add_action( 'wp_ajax_nopriv_bookly_custom_search_availability', array( 'BooklyPro\Frontend\Modules\CustomSearchForm\Ajax', 'searchAvailability' ) );
        add_action( 'wp_ajax_bookly_custom_continue_booking', array( 'BooklyPro\Frontend\Modules\CustomSearchForm\Ajax', 'continueBooking' ) );
        add_action( 'wp_ajax_nopriv_bookly_custom_continue_booking', array( 'BooklyPro\Frontend\Modules\CustomSearchForm\Ajax', 'continueBooking' ) );
        
        // Enqueue scripts and styles
        add_action( 'wp_enqueue_scripts', array( __CLASS__, 'enqueueAssets' ) );
    }
    
    /**
     * Render shortcode
     */
    public static function renderShortcode( $atts )
    {
        $atts = shortcode_atts( array(
            'show_calendar' => 'yes',
            'show_time_slots' => 'yes',
            'show_duration' => 'yes',
            'default_duration' => '2',
            'available_durations' => '2,3,4,5,6',
            'time_slot_step' => '30',
            'min_time' => '09:00',
            'max_time' => '21:00',
            'title' => __( 'Find Your Perfect Room', 'bookly' ),
            'subtitle' => __( 'Select your preferred date, time, and duration to see available rooms.', 'bookly' ),
            'search_button_text' => __( 'Search Rooms', 'bookly' ),
            'theme' => 'default', // default, compact, minimal
            'show_icons' => 'yes',
            'auto_search' => 'no', // Auto search when all fields are filled
        ), $atts );

        // Validate attributes
        $atts = self::validateShortcodeAttributes( $atts );

        // Start output buffering
        ob_start();

        // Include template
        include __DIR__ . '/templates/search-form.php';

        return ob_get_clean();
    }

    /**
     * Validate shortcode attributes
     *
     * @param array $atts
     * @return array
     */
    private static function validateShortcodeAttributes( $atts )
    {
        // Validate boolean attributes
        $boolean_attrs = array( 'show_calendar', 'show_time_slots', 'show_duration', 'show_icons', 'auto_search' );
        foreach ( $boolean_attrs as $attr ) {
            $atts[ $attr ] = in_array( strtolower( $atts[ $attr ] ), array( 'yes', 'true', '1' ) ) ? 'yes' : 'no';
        }

        // Validate numeric attributes
        $atts['default_duration'] = max( 1, intval( $atts['default_duration'] ) );
        $atts['time_slot_step'] = max( 15, min( 60, intval( $atts['time_slot_step'] ) ) );

        // Validate time format
        if ( ! preg_match( '/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/', $atts['min_time'] ) ) {
            $atts['min_time'] = '09:00';
        }
        if ( ! preg_match( '/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/', $atts['max_time'] ) ) {
            $atts['max_time'] = '21:00';
        }

        // Validate available durations
        $durations = explode( ',', $atts['available_durations'] );
        $valid_durations = array();
        foreach ( $durations as $duration ) {
            $duration = trim( $duration );
            if ( is_numeric( $duration ) && $duration > 0 && $duration <= 24 ) {
                $valid_durations[] = $duration;
            }
        }
        if ( empty( $valid_durations ) ) {
            $valid_durations = array( '2', '3', '4', '5', '6' );
        }
        $atts['available_durations'] = implode( ',', $valid_durations );

        // Validate theme
        $valid_themes = array( 'default', 'compact', 'minimal' );
        if ( ! in_array( $atts['theme'], $valid_themes ) ) {
            $atts['theme'] = 'default';
        }

        return $atts;
    }
    
    /**
     * Enqueue scripts and styles
     */
    public static function enqueueAssets()
    {
        // Only enqueue on pages that have our shortcode
        global $post;
        if ( is_a( $post, 'WP_Post' ) && has_shortcode( $post->post_content, 'bookly-custom-search-form' ) ) {
            
            // Enqueue Bookly dependencies
            wp_enqueue_script( 'bookly-frontend-globals' );
            wp_enqueue_script( 'jquery-ui-datepicker' );
            wp_enqueue_style( 'jquery-ui-datepicker' );
            
            // Enqueue our custom assets
            wp_enqueue_script( 
                'bookly-custom-search-form', 
                plugins_url( 'resources/js/custom-search-form.js', __FILE__ ), 
                array( 'jquery', 'bookly-frontend-globals', 'jquery-ui-datepicker' ), 
                '1.0.0', 
                true 
            );
            
            wp_enqueue_style( 
                'bookly-custom-search-form', 
                plugins_url( 'resources/css/custom-search-form.css', __FILE__ ), 
                array(), 
                '1.0.0' 
            );
            
            // Localize script
            wp_localize_script( 'bookly-custom-search-form', 'BooklyCustomSearchForm', array(
                'ajaxUrl' => admin_url( 'admin-ajax.php' ),
                'nonce' => wp_create_nonce( 'bookly_custom_search_nonce' ),
                'l10n' => array(
                    'selectDate' => __( 'Select Date', 'bookly' ),
                    'selectTime' => __( 'Select Time', 'bookly' ),
                    'selectDuration' => __( 'Select Duration', 'bookly' ),
                    'searchButton' => __( 'Search Available Rooms', 'bookly' ),
                    'noAvailability' => __( 'No rooms available for the selected date and time.', 'bookly' ),
                    'loading' => __( 'Searching...', 'bookly' ),
                    'hours' => __( 'hours', 'bookly' ),
                    'hour' => __( 'hour', 'bookly' ),
                    'selectRoom' => __( 'Select Room', 'bookly' ),
                    'price' => __( 'Price', 'bookly' ),
                    'duration' => __( 'Duration', 'bookly' ),
                ),
                'dateFormat' => BooklyLib\Utils\DateTime::convertFormat( 'date', BooklyLib\Utils\DateTime::FORMAT_JQUERY_DATEPICKER ),
                'timeFormat' => BooklyLib\Utils\DateTime::convertFormat( 'time', BooklyLib\Utils\DateTime::FORMAT_MOMENT_JS ),
                'maxDaysForBooking' => BooklyLib\Config::getMaximumAvailableDaysForBooking(),
            ) );
        }
    }
}
