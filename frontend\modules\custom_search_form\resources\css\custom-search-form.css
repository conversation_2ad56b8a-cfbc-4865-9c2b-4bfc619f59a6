/**
 * Custom Search Form Styles
 */

/* CSS Custom Properties for theming */
.bookly-custom-search-form {
    --primary-color: #3498db;
    --primary-hover: #2980b9;
    --success-color: #27ae60;
    --success-hover: #229954;
    --text-color: #2c3e50;
    --text-muted: #7f8c8d;
    --border-color: #e1e8ed;
    --background: #fff;
    --border-radius: 8px;
    --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* Main Container */
.bookly-custom-search-form {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

/* Search Container */
.bookly-search-container {
    background: var(--background);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 30px;
    margin-bottom: 30px;
}

/* Theme Variations */
.bookly-theme-compact .bookly-search-container {
    padding: 20px;
    margin-bottom: 20px;
}

.bookly-theme-compact .bookly-search-title {
    font-size: 22px;
    margin-bottom: 8px;
}

.bookly-theme-compact .bookly-search-subtitle {
    font-size: 14px;
    margin-bottom: 20px;
}

.bookly-theme-minimal .bookly-search-container {
    background: transparent;
    box-shadow: none;
    border: 2px solid var(--border-color);
    padding: 25px;
}

.bookly-theme-minimal .bookly-search-title {
    font-size: 24px;
    font-weight: 600;
}

.bookly-theme-minimal .bookly-search-row {
    gap: 15px;
}

.bookly-search-title {
    font-size: 28px;
    font-weight: 700;
    color: var(--text-color);
    margin: 0 0 10px 0;
    text-align: center;
}

.bookly-search-subtitle {
    font-size: 16px;
    color: var(--text-muted);
    margin: 0 0 30px 0;
    text-align: center;
}

/* Search Form */
.bookly-search-form {
    display: flex;
    flex-direction: column;
}

.bookly-search-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    align-items: end;
}

.bookly-search-field {
    display: flex;
    flex-direction: column;
}

.bookly-search-label {
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.bookly-search-label i {
    color: var(--primary-color);
    font-size: 16px;
}

/* Icon fallbacks for when Bookly icons aren't available */
.bookly-icon-calendar:before { content: "📅"; }
.bookly-icon-clock:before { content: "🕐"; }
.bookly-icon-time:before { content: "⏱️"; }
.bookly-icon-search:before { content: "🔍"; }
.bookly-icon-spinner:before { content: "⏳"; }
.bookly-icon-check:before { content: "✓"; }
.bookly-icon-money:before { content: "💰"; }
.bookly-icon-service:before { content: "🏠"; }
.bookly-icon-info:before { content: "ℹ️"; }
.bookly-icon-warning:before { content: "⚠️"; }

.bookly-search-input,
.bookly-search-select {
    padding: 12px 16px;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: #fff;
    color: #2c3e50;
}

.bookly-search-input:focus,
.bookly-search-select:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.bookly-search-input::placeholder {
    color: #95a5a6;
}

/* Search Button */
.bookly-search-button-field {
    display: flex;
    align-items: end;
}

.bookly-search-button {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
    min-height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.bookly-search-button:hover:not(:disabled) {
    background: linear-gradient(135deg, #2980b9, #1f5f8b);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.bookly-search-button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

/* Loading States */
.bookly-search-loading {
    text-align: center;
    padding: 40px 20px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.bookly-loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e1e8ed;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: bookly-spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes bookly-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Results */
.bookly-search-results {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    padding: 30px;
}

.bookly-results-title {
    font-size: 24px;
    font-weight: 700;
    color: #2c3e50;
    margin: 0 0 20px 0;
    text-align: center;
}

.bookly-results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

/* Service Cards */
.bookly-service-card {
    background: #fff;
    border: 2px solid #e1e8ed;
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.bookly-service-card:hover {
    border-color: #3498db;
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(52, 152, 219, 0.15);
}

.bookly-service-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.bookly-service-title {
    font-size: 18px;
    font-weight: 700;
    color: #2c3e50;
    margin: 0;
    flex: 1;
}

.bookly-service-duration {
    background: #3498db;
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    white-space: nowrap;
}

.bookly-service-card-body {
    margin-bottom: 20px;
}

.bookly-service-info {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.bookly-service-time,
.bookly-service-price {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #7f8c8d;
    font-size: 14px;
}

.bookly-service-time i,
.bookly-service-price i {
    color: #3498db;
    width: 16px;
}

.bookly-service-price {
    font-weight: 600;
    color: #27ae60;
    font-size: 16px;
}

.bookly-select-service-button {
    background: linear-gradient(135deg, #27ae60, #229954);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
}

.bookly-select-service-button:hover {
    background: linear-gradient(135deg, #229954, #1e7e34);
    transform: translateY(-1px);
}

/* No Results & Error States */
.bookly-no-results,
.bookly-search-error {
    text-align: center;
    padding: 40px 20px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.bookly-no-results-icon,
.bookly-error-icon {
    font-size: 48px;
    color: #95a5a6;
    margin-bottom: 20px;
}

.bookly-error-icon {
    color: #e74c3c;
}

.bookly-no-results h4,
.bookly-search-error h4 {
    font-size: 20px;
    color: #2c3e50;
    margin: 0 0 10px 0;
}

.bookly-no-results p,
.bookly-search-error p {
    color: #7f8c8d;
    margin: 0 0 20px 0;
    line-height: 1.6;
}

.bookly-search-again-button,
.bookly-try-again-button {
    background: #3498db;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.bookly-search-again-button:hover,
.bookly-try-again-button:hover {
    background: #2980b9;
}

/* Responsive Design */
@media (max-width: 768px) {
    .bookly-custom-search-form {
        padding: 15px;
    }
    
    .bookly-search-container {
        padding: 20px;
    }
    
    .bookly-search-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .bookly-search-title {
        font-size: 24px;
    }
    
    .bookly-results-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .bookly-service-card {
        padding: 15px;
    }
}

@media (max-width: 480px) {
    .bookly-search-container {
        padding: 15px;
    }
    
    .bookly-search-title {
        font-size: 20px;
    }
    
    .bookly-search-subtitle {
        font-size: 14px;
    }
}
