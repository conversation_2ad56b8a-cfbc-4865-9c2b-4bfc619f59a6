<?php
/**
 * Booking Confirmation Template
 * Shows booking details and allows user to proceed with the booking
 */

defined( 'ABSPATH' ) || exit;

// Get booking parameters
$service_id = (int) $_GET['service_id'];
$staff_id = (int) $_GET['staff_id'];
$datetime = $_GET['datetime'];
$booking_token = $_GET['booking_token'];

// Verify token
if ( ! wp_verify_nonce( $booking_token, 'booking_confirm_' . $service_id . '_' . $staff_id ) ) {
    echo '<div class="bookly-error">Invalid booking token. Please try again.</div>';
    return;
}

// Get service and staff details
$service = BooklyLib\Entities\Service::find( $service_id );
$staff = BooklyLib\Entities\Staff::find( $staff_id );

if ( ! $service || ! $staff ) {
    echo '<div class="bookly-error">Service or staff not found. Please try again.</div>';
    return;
}

// Get price
$staff_service = BooklyLib\Entities\StaffService::query()
    ->where( 'staff_id', $staff_id )
    ->where( 'service_id', $service_id )
    ->findOne();

$price = $staff_service ? $staff_service->getPrice() : 0;

// Format datetime
$booking_datetime = date_create( $datetime );
$formatted_date = $booking_datetime ? $booking_datetime->format( 'F j, Y' ) : $datetime;
$formatted_time = $booking_datetime ? $booking_datetime->format( 'g:i A' ) : '';
$duration_hours = $service->getDuration() / 3600;
$end_datetime = $booking_datetime ? $booking_datetime->add( new DateInterval( 'PT' . $service->getDuration() . 'S' ) ) : null;
$formatted_end_time = $end_datetime ? $end_datetime->format( 'g:i A' ) : '';

?>

<div class="bookly-custom-search-form">
    <div class="bookly-booking-confirmation">
        <div class="bookly-confirmation-header">
            <h3>🎉 Booking Confirmation</h3>
            <p>Please review your booking details below:</p>
        </div>

        <div class="bookly-booking-details">
            <div class="bookly-detail-row">
                <span class="bookly-detail-label">
                    <i class="bookly-icon-service"></i>
                    Room/Service:
                </span>
                <span class="bookly-detail-value"><?php echo esc_html( $service->getTitle() ); ?></span>
            </div>

            <div class="bookly-detail-row">
                <span class="bookly-detail-label">
                    <i class="bookly-icon-staff"></i>
                    Staff:
                </span>
                <span class="bookly-detail-value"><?php echo esc_html( $staff->getFullName() ); ?></span>
            </div>

            <div class="bookly-detail-row">
                <span class="bookly-detail-label">
                    <i class="bookly-icon-calendar"></i>
                    Date:
                </span>
                <span class="bookly-detail-value"><?php echo esc_html( $formatted_date ); ?></span>
            </div>

            <div class="bookly-detail-row">
                <span class="bookly-detail-label">
                    <i class="bookly-icon-time"></i>
                    Time:
                </span>
                <span class="bookly-detail-value">
                    <?php echo esc_html( $formatted_time ); ?>
                    <?php if ( $formatted_end_time ) : ?>
                        - <?php echo esc_html( $formatted_end_time ); ?>
                    <?php endif; ?>
                    (<?php echo esc_html( number_format( $duration_hours, 1 ) ); ?> hours)
                </span>
            </div>

            <div class="bookly-detail-row">
                <span class="bookly-detail-label">
                    <i class="bookly-icon-price"></i>
                    Price:
                </span>
                <span class="bookly-detail-value bookly-price">
                    <?php echo BooklyLib\Utils\Price::format( $price ); ?>
                </span>
            </div>
        </div>

        <div class="bookly-confirmation-actions">
            <button type="button" class="bookly-btn bookly-btn-primary bookly-proceed-booking" 
                    data-service-id="<?php echo esc_attr( $service_id ); ?>"
                    data-staff-id="<?php echo esc_attr( $staff_id ); ?>"
                    data-datetime="<?php echo esc_attr( $datetime ); ?>">
                <i class="bookly-icon-check"></i>
                Proceed with Booking
            </button>
            
            <button type="button" class="bookly-btn bookly-btn-secondary bookly-back-to-search">
                <i class="bookly-icon-back"></i>
                Back to Search
            </button>
        </div>

        <div class="bookly-confirmation-note">
            <p><strong>Note:</strong> This is a temporary confirmation page. In a full implementation, clicking "Proceed with Booking" would continue to Bookly's standard booking flow for customer details and payment.</p>
        </div>
    </div>
</div>

<style>
.bookly-booking-confirmation {
    max-width: 600px;
    margin: 0 auto;
    padding: 30px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.bookly-confirmation-header {
    text-align: center;
    margin-bottom: 30px;
}

.bookly-confirmation-header h3 {
    color: #2c3e50;
    font-size: 24px;
    margin-bottom: 10px;
}

.bookly-confirmation-header p {
    color: #7f8c8d;
    font-size: 16px;
}

.bookly-booking-details {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 30px;
}

.bookly-detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #e9ecef;
}

.bookly-detail-row:last-child {
    border-bottom: none;
}

.bookly-detail-label {
    font-weight: 600;
    color: #495057;
    display: flex;
    align-items: center;
    gap: 8px;
}

.bookly-detail-value {
    color: #212529;
    font-weight: 500;
}

.bookly-detail-value.bookly-price {
    font-size: 18px;
    font-weight: 700;
    color: #28a745;
}

.bookly-confirmation-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-bottom: 20px;
}

.bookly-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.bookly-btn-primary {
    background: #007cba;
    color: white;
}

.bookly-btn-primary:hover {
    background: #005a87;
}

.bookly-btn-secondary {
    background: #6c757d;
    color: white;
}

.bookly-btn-secondary:hover {
    background: #545b62;
}

.bookly-confirmation-note {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 15px;
    text-align: center;
}

.bookly-confirmation-note p {
    margin: 0;
    color: #856404;
    font-size: 14px;
}

/* Icons */
.bookly-icon-service::before { content: "🏢"; }
.bookly-icon-staff::before { content: "👤"; }
.bookly-icon-calendar::before { content: "📅"; }
.bookly-icon-time::before { content: "🕐"; }
.bookly-icon-price::before { content: "💰"; }
.bookly-icon-check::before { content: "✓"; }
.bookly-icon-back::before { content: "←"; }

@media (max-width: 768px) {
    .bookly-booking-confirmation {
        padding: 20px;
        margin: 10px;
    }
    
    .bookly-confirmation-actions {
        flex-direction: column;
    }
    
    .bookly-detail-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
}
</style>

<script>
jQuery(document).ready(function($) {
    $('.bookly-back-to-search').on('click', function() {
        // Remove booking parameters from URL and reload
        var url = window.location.href.split('?')[0];
        window.location.href = url;
    });
    
    $('.bookly-proceed-booking').on('click', function() {
        alert('In a full implementation, this would continue to Bookly\'s standard booking flow for customer details and payment.');
    });
});
</script>
