<?php
/**
 * Test page for Custom Search Form
 * 
 * This file can be used to test the custom search form functionality
 * Copy this to your theme or create a page with this content
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

get_header(); ?>

<div class="container" style="max-width: 1200px; margin: 0 auto; padding: 20px;">
    
    <h1>Bookly Custom Search Form - Test Page</h1>
    
    <div style="margin-bottom: 40px;">
        <h2>Basic Form</h2>
        <?php echo do_shortcode( '[bookly-custom-search-form]' ); ?>
    </div>
    
    <div style="margin-bottom: 40px;">
        <h2>Compact Theme</h2>
        <?php echo do_shortcode( '[bookly-custom-search-form theme="compact" title="Quick Room Search" subtitle="Find available rooms quickly"]' ); ?>
    </div>
    
    <div style="margin-bottom: 40px;">
        <h2>Minimal Theme</h2>
        <?php echo do_shortcode( '[bookly-custom-search-form theme="minimal" show_icons="no"]' ); ?>
    </div>
    
    <div style="margin-bottom: 40px;">
        <h2>Custom Configuration</h2>
        <?php echo do_shortcode( '[bookly-custom-search-form 
            available_durations="1,2,3,4,6,8" 
            default_duration="3"
            min_time="08:00" 
            max_time="22:00"
            time_slot_step="15"
            search_button_text="Find Rooms Now"
            title="Premium Room Booking"
            subtitle="Select your preferred time and duration for premium room booking"
        ]' ); ?>
    </div>
    
    <div style="margin-bottom: 40px;">
        <h2>Testing Instructions</h2>
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #007cba;">
            <h3>How to Test</h3>
            <ol>
                <li><strong>Basic Functionality:</strong>
                    <ul>
                        <li>Select a date (today or future)</li>
                        <li>Choose a time slot</li>
                        <li>Pick a duration</li>
                        <li>Click "Search Rooms"</li>
                    </ul>
                </li>
                <li><strong>Expected Results:</strong>
                    <ul>
                        <li>Loading indicator should appear</li>
                        <li>Available rooms should be displayed as cards</li>
                        <li>Each card should show room name, time, duration, and price</li>
                        <li>Clicking "Select Room" should continue to Bookly's booking flow</li>
                    </ul>
                </li>
                <li><strong>Error Cases to Test:</strong>
                    <ul>
                        <li>Submit form without selecting all fields</li>
                        <li>Select a time when no rooms are available</li>
                        <li>Test with different durations</li>
                    </ul>
                </li>
                <li><strong>Responsive Testing:</strong>
                    <ul>
                        <li>Test on mobile devices</li>
                        <li>Test on tablets</li>
                        <li>Resize browser window</li>
                    </ul>
                </li>
            </ol>
        </div>
    </div>
    
    <div style="margin-bottom: 40px;">
        <h2>Debug Information</h2>
        <div style="background: #fff3cd; padding: 15px; border-radius: 8px; border: 1px solid #ffeaa7;">
            <h4>System Requirements Check</h4>
            <ul>
                <li>WordPress Version: <?php echo get_bloginfo( 'version' ); ?></li>
                <li>PHP Version: <?php echo PHP_VERSION; ?></li>
                <li>Bookly Pro Active: <?php echo class_exists( 'BooklyPro\Lib\Boot' ) ? '✅ Yes' : '❌ No'; ?></li>
                <li>jQuery Available: <span id="jquery-check">Checking...</span></li>
                <li>AJAX URL: <?php echo admin_url( 'admin-ajax.php' ); ?></li>
            </ul>
        </div>
    </div>
    
    <div style="margin-bottom: 40px;">
        <h2>Troubleshooting</h2>
        <div style="background: #f8d7da; padding: 15px; border-radius: 8px; border: 1px solid #f5c6cb;">
            <h4>Common Issues</h4>
            <ul>
                <li><strong>No search results:</strong> Check if you have services configured in Bookly with matching durations</li>
                <li><strong>JavaScript errors:</strong> Check browser console for errors</li>
                <li><strong>Styling issues:</strong> Check for CSS conflicts with your theme</li>
                <li><strong>AJAX errors:</strong> Check WordPress debug log</li>
            </ul>
            
            <h4>Debug Steps</h4>
            <ol>
                <li>Open browser developer tools (F12)</li>
                <li>Go to Console tab</li>
                <li>Try searching for rooms</li>
                <li>Check for any error messages</li>
                <li>Go to Network tab and check AJAX requests</li>
            </ol>
        </div>
    </div>
    
</div>

<script>
jQuery(document).ready(function($) {
    // Check if jQuery is working
    $('#jquery-check').text('✅ Working');
    
    // Add debug event listeners
    $(document).on('bookly_search_started', function(event, data) {
        console.log('Search started:', data);
    });
    
    $(document).on('bookly_search_completed', function(event, data) {
        console.log('Search completed:', data);
    });
    
    $(document).on('bookly_service_selected', function(event, data) {
        console.log('Service selected:', data);
    });
    
    // Test AJAX connectivity
    $.ajax({
        url: '<?php echo admin_url( 'admin-ajax.php' ); ?>',
        type: 'POST',
        data: {
            action: 'heartbeat',
            _wpnonce: '<?php echo wp_create_nonce( 'heartbeat-nonce' ); ?>'
        },
        success: function() {
            console.log('✅ AJAX connectivity test passed');
        },
        error: function() {
            console.error('❌ AJAX connectivity test failed');
        }
    });
});
</script>

<?php get_footer(); ?>
