<?php
/**
 * Custom Search Form Template
 *
 * @var array $atts Shortcode attributes
 */

defined( 'ABSPATH' ) || exit;

// Import Bookly classes
use Bookly\Lib as BooklyLib;

// Check if we need to show booking confirmation or proceed with booking
$bookly_booking = isset( $_GET['bookly_booking'] ) ? $_GET['bookly_booking'] : '';
$bookly_proceed = isset( $_GET['bookly_proceed'] ) ? $_GET['bookly_proceed'] : '';
$service_id = isset( $_GET['service_id'] ) ? (int) $_GET['service_id'] : 0;
$staff_id = isset( $_GET['staff_id'] ) ? (int) $_GET['staff_id'] : 0;
$datetime = isset( $_GET['datetime'] ) ? urldecode( $_GET['datetime'] ) : '';
$booking_token = isset( $_GET['booking_token'] ) ? $_GET['booking_token'] : '';

if ( $bookly_booking && $service_id && $staff_id && $datetime && $booking_token ) {
    // Verify token
    if ( wp_verify_nonce( $booking_token, 'bookly_booking_' . $service_id . '_' . $staff_id . '_' . $datetime ) ) {
        if ( $bookly_proceed ) {
            // Create a modern booking form request with pre-populated data
            $form_id = 'custom_booking_' . uniqid();

            // Create the items array for the modern booking form
            $items = array(
                array(
                    'type' => 'appointment',
                    'service_id' => $service_id,
                    'staff_id' => $staff_id,
                    'slot' => array(
                        'slot' => array( array( $service_id, $staff_id, $datetime, null ) )
                    ),
                    'number_of_persons' => 1,
                    'units' => 1,
                    'extras' => array(),
                    'custom_fields' => array()
                )
            );

            // Render the modern booking form directly
            echo '<div class="bookly-proceed-booking">';
            echo '<h3>Complete Your Booking</h3>';
            echo '<p>Please fill in your details below to complete the booking:</p>';

            // Load modern booking form assets
            \BooklyPro\Frontend\Modules\ModernBookingForm\Form::render();

            // Create the form HTML with pre-populated data
            ?>
            <div id="bookly-modern-booking-form" data-form-id="<?php echo esc_attr( $form_id ); ?>">
                <div class="bookly-modern-form-container">
                    <!-- This will be populated by JavaScript with the details step -->
                </div>
            </div>

            <script>
            jQuery(document).ready(function($) {
                // Pre-populate the modern booking form with our data
                if (typeof BooklyModernBookingForm !== 'undefined') {
                    var formData = {
                        items: <?php echo json_encode( $items ); ?>,
                        step: 'details', // Start directly at details step
                        form_id: '<?php echo esc_js( $form_id ); ?>'
                    };

                    console.log('Initializing modern booking form with data:', formData);

                    // Initialize the modern booking form
                    BooklyModernBookingForm.init(formData);
                } else {
                    console.error('BooklyModernBookingForm not loaded');

                    // Fallback: redirect to regular booking form
                    setTimeout(function() {
                        window.location.href = '<?php echo esc_js( add_query_arg( array( 'fallback' => '1' ), $_SERVER['REQUEST_URI'] ) ); ?>';
                    }, 2000);
                }
            });
            </script>
            <?php

            echo '</div>';
            return;
        } else {
            // Show booking summary
            include __DIR__ . '/booking-summary.php';
            return;
        }
    }
}

// Generate unique form ID
$form_id = 'bookly-custom-search-' . uniqid();
?>

<div id="<?php echo esc_attr( $form_id ); ?>" class="bookly-custom-search-form">
    
    <!-- Search Form -->
    <div class="bookly-search-container bookly-theme-<?php echo esc_attr( $atts['theme'] ); ?>">
        <h3 class="bookly-search-title"><?php echo esc_html( $atts['title'] ); ?></h3>
        <p class="bookly-search-subtitle"><?php echo esc_html( $atts['subtitle'] ); ?></p>
        
        <form class="bookly-search-form" data-form-id="<?php echo esc_attr( $form_id ); ?>">
            <div class="bookly-search-row">
                
                <?php if ( $atts['show_calendar'] === 'yes' ) : ?>
                <!-- Date Selection -->
                <div class="bookly-search-field">
                    <label for="<?php echo esc_attr( $form_id ); ?>-date" class="bookly-search-label">
                        <i class="bookly-icon-calendar"></i>
                        <?php esc_html_e( 'Date', 'bookly' ); ?>
                    </label>
                    <input 
                        type="text" 
                        id="<?php echo esc_attr( $form_id ); ?>-date" 
                        name="search_date" 
                        class="bookly-search-input bookly-date-picker" 
                        placeholder="<?php esc_attr_e( 'Select Date', 'bookly' ); ?>"
                        readonly
                        required
                    />
                </div>
                <?php endif; ?>
                
                <?php if ( $atts['show_time_slots'] === 'yes' ) : ?>
                <!-- Time Selection -->
                <div class="bookly-search-field">
                    <label for="<?php echo esc_attr( $form_id ); ?>-time" class="bookly-search-label">
                        <i class="bookly-icon-clock"></i>
                        <?php esc_html_e( 'Time', 'bookly' ); ?>
                    </label>
                    <select 
                        id="<?php echo esc_attr( $form_id ); ?>-time" 
                        name="search_time" 
                        class="bookly-search-select"
                        required
                    >
                        <option value=""><?php esc_html_e( 'Select Time', 'bookly' ); ?></option>
                        <?php
                        // Generate time slots
                        $min_time = $atts['min_time'];
                        $max_time = $atts['max_time'];
                        $step_minutes = (int) $atts['time_slot_step'];
                        
                        $current_time = strtotime( $min_time );
                        $end_time = strtotime( $max_time );
                        
                        while ( $current_time <= $end_time ) {
                            $time_value = date( 'H:i', $current_time );
                            $time_display = date( 'g:i A', $current_time );
                            echo '<option value="' . esc_attr( $time_value ) . '">' . esc_html( $time_display ) . '</option>';
                            $current_time += ( $step_minutes * 60 );
                        }
                        ?>
                    </select>
                </div>
                <?php endif; ?>
                
                <?php if ( $atts['show_duration'] === 'yes' ) : ?>
                <!-- Duration Selection -->
                <div class="bookly-search-field">
                    <label for="<?php echo esc_attr( $form_id ); ?>-duration" class="bookly-search-label">
                        <i class="bookly-icon-time"></i>
                        <?php esc_html_e( 'Duration', 'bookly' ); ?>
                    </label>
                    <select 
                        id="<?php echo esc_attr( $form_id ); ?>-duration" 
                        name="search_duration" 
                        class="bookly-search-select"
                        required
                    >
                        <option value=""><?php esc_html_e( 'Select Duration', 'bookly' ); ?></option>
                        <?php
                        $available_durations = explode( ',', $atts['available_durations'] );
                        $default_duration = $atts['default_duration'];
                        
                        foreach ( $available_durations as $duration ) {
                            $duration = trim( $duration );
                            if ( is_numeric( $duration ) && $duration > 0 ) {
                                $selected = ( $duration == $default_duration ) ? ' selected' : '';
                                $label = ( $duration == 1 ) ? 
                                    sprintf( __( '%s hour', 'bookly' ), $duration ) : 
                                    sprintf( __( '%s hours', 'bookly' ), $duration );
                                echo '<option value="' . esc_attr( $duration ) . '"' . $selected . '>' . esc_html( $label ) . '</option>';
                            }
                        }
                        ?>
                    </select>
                </div>
                <?php endif; ?>
                
                <!-- Search Button -->
                <div class="bookly-search-field bookly-search-button-field">
                    <button type="submit" class="bookly-search-button">
                        <span class="bookly-search-button-text">
                            <?php if ( $atts['show_icons'] === 'yes' ) : ?>
                                <i class="bookly-icon bookly-icon-search"></i>
                            <?php endif; ?>
                            <?php echo esc_html( $atts['search_button_text'] ); ?>
                        </span>
                        <span class="bookly-search-button-loading" style="display: none;">
                            <i class="bookly-icon bookly-icon-spinner"></i>
                            <?php esc_html_e( 'Searching...', 'bookly' ); ?>
                        </span>
                    </button>
                </div>
                
            </div>
        </form>
    </div>
    
    <!-- Loading Indicator -->
    <div class="bookly-search-loading" style="display: none;">
        <div class="bookly-loading-spinner"></div>
        <p><?php esc_html_e( 'Searching for available rooms...', 'bookly' ); ?></p>
    </div>
    
    <!-- Results Container -->
    <div class="bookly-search-results" style="display: none;">
        <h4 class="bookly-results-title"><?php esc_html_e( 'Available Rooms', 'bookly' ); ?></h4>
        <div class="bookly-results-grid"></div>
    </div>
    
    <!-- No Results Message -->
    <div class="bookly-no-results" style="display: none;">
        <div class="bookly-no-results-icon">
            <i class="bookly-icon-info"></i>
        </div>
        <h4><?php esc_html_e( 'No Rooms Available', 'bookly' ); ?></h4>
        <p class="bookly-no-results-message"><?php esc_html_e( 'Sorry, no rooms are available for your selected date and time. Please try a different time or date.', 'bookly' ); ?></p>
        <button type="button" class="bookly-search-again-button">
            <?php esc_html_e( 'Search Again', 'bookly' ); ?>
        </button>
    </div>
    
    <!-- Error Message -->
    <div class="bookly-search-error" style="display: none;">
        <div class="bookly-error-icon">
            <i class="bookly-icon-warning"></i>
        </div>
        <h4><?php esc_html_e( 'Search Error', 'bookly' ); ?></h4>
        <p class="bookly-error-message"></p>
        <button type="button" class="bookly-try-again-button">
            <?php esc_html_e( 'Try Again', 'bookly' ); ?>
        </button>
    </div>

    <!-- Debug Information -->
    <div class="bookly-search-debug" style="margin-top: 20px; padding: 10px; background: #f0f0f0; border: 1px solid #ccc; font-family: monospace; font-size: 12px; display: block;">
        <strong>Debug Information:</strong><br>
        <div class="bookly-debug-content"></div>
    </div>

</div>

<!-- Service Card Template (hidden) -->
<script type="text/template" id="bookly-service-card-template">
    <div class="bookly-service-card" data-service-id="{{service_id}}" data-staff-id="{{staff_id}}">
        <div class="bookly-service-card-header">
            <h5 class="bookly-service-title">{{staff_name}}</h5>
            <div class="bookly-service-duration">{{duration_formatted}}</div>
        </div>
        <div class="bookly-service-card-body">
            <div class="bookly-service-info">
                <div class="bookly-service-time">
                    <i class="bookly-icon bookly-icon-clock"></i>
                    <span><?php esc_html_e( 'Time:', 'bookly' ); ?> {{start_time}} - {{end_time}}</span>
                </div>
                <div class="bookly-service-price">
                    <i class="bookly-icon bookly-icon-money"></i>
                    <span class="bookly-price-amount">{{price}}</span>
                </div>
                <div class="bookly-service-type">
                    <i class="bookly-icon bookly-icon-service"></i>
                    <span>{{service_title}}</span>
                </div>
            </div>
        </div>
        <div class="bookly-service-card-footer">
            <button type="button" class="bookly-select-service-button" data-booking-url="{{booking_url}}" data-service-id="{{service_id}}" data-staff-id="{{staff_id}}">
                <i class="bookly-icon bookly-icon-check"></i>
                <?php esc_html_e( 'Select This Room', 'bookly' ); ?>
            </button>
        </div>
    </div>
</script>

<script>
jQuery(document).ready(function($) {
    // Intercept AJAX calls to show debug info
    var originalAjax = $.ajax;
    $.ajax = function(options) {
        if (options.url && options.url.indexOf('bookly_custom_search_form_search') !== -1) {
            var originalSuccess = options.success;
            options.success = function(response) {
                // Show debug info
                var debugDiv = $('.bookly-search-debug .bookly-debug-content');
                var debugInfo = '';

                if (response.data && response.data.debug) {
                    debugInfo += 'Combinations checked: ' + (response.data.debug.total_combinations_checked || 0) + '<br>';
                    debugInfo += 'Staff checked: ' + (response.data.debug.total_staff_checked || 0) + '<br>';
                    debugInfo += 'Search datetime: ' + (response.data.debug.search_datetime || 'N/A') + '<br>';
                    debugInfo += 'Debug enabled: ' + (response.data.debug.debug_enabled ? 'Yes' : 'No') + '<br>';
                }

                if (response.data && response.data.message) {
                    debugInfo += 'Message: ' + response.data.message + '<br>';
                }

                if (response.data && response.data.services) {
                    debugInfo += 'Services found: ' + response.data.services.length + '<br>';
                }

                debugDiv.html(debugInfo || 'No debug information available');

                // Also log to console
                console.log('Bookly Search Debug:', response.data);

                // Call original success handler
                if (originalSuccess) {
                    originalSuccess.apply(this, arguments);
                }
            };
        }
        return originalAjax.apply(this, arguments);
    };
});
</script>
