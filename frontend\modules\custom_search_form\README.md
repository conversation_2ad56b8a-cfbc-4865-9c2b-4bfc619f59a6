# Bookly Custom Search Form

A custom booking search flow for Bookly Pro that allows users to search by date, time, and duration first, then shows available rooms/services.

## Features

- **Custom Flow**: Date + Time + Duration → Available Services → Extras → Details → Payment
- **Responsive Design**: Works on desktop, tablet, and mobile devices
- **Integration**: Seamlessly integrates with existing Bookly booking flow
- **Customizable**: Multiple shortcode attributes for customization

## Installation

1. Copy the `custom_search_form` folder to your Bookly Pro plugin directory
2. The module will be automatically loaded when Bookly Pro is active

## Usage

### Basic Shortcode

```
[bookly-custom-search-form]
```

### Advanced Shortcode with Options

```
[bookly-custom-search-form 
    show_calendar="yes" 
    show_time_slots="yes" 
    show_duration="yes"
    default_duration="3"
    available_durations="2,3,4,5,6"
    time_slot_step="30"
    min_time="09:00"
    max_time="21:00"
]
```

## Shortcode Attributes

| Attribute | Default | Description |
|-----------|---------|-------------|
| `show_calendar` | `yes` | Show/hide the date picker |
| `show_time_slots` | `yes` | Show/hide the time selection |
| `show_duration` | `yes` | Show/hide the duration selection |
| `default_duration` | `2` | Default selected duration in hours |
| `available_durations` | `2,3,4,5,6` | Comma-separated list of available durations |
| `time_slot_step` | `30` | Time slot step in minutes |
| `min_time` | `09:00` | Earliest available time |
| `max_time` | `21:00` | Latest available time |

## How It Works

1. **User Input**: User selects date, time, and duration
2. **Search**: System searches for available staff/services matching the criteria
3. **Results**: Available rooms are displayed as cards with details
4. **Selection**: User selects a room and continues to Bookly's standard flow
5. **Booking**: Standard Bookly flow continues (extras → details → payment)

## Technical Details

### Files Structure

```
custom_search_form/
├── Module.php              # Main module class
├── Ajax.php                # AJAX handlers
├── templates/
│   └── search-form.php     # Main template
├── resources/
│   ├── js/
│   │   └── custom-search-form.js
│   └── css/
│       └── custom-search-form.css
└── README.md
```

### Database Integration

The module uses Bookly's existing database structure:
- `bookly_services` - Service definitions
- `bookly_staff` - Staff/room definitions  
- `bookly_staff_services` - Staff-service relationships
- `bookly_appointments` - Existing bookings
- `bookly_staff_schedule_items` - Working hours

### Availability Logic

1. **Duration Matching**: Finds services with matching duration (±15 min tolerance)
2. **Staff Assignment**: Gets staff members assigned to matching services
3. **Conflict Check**: Checks for existing appointments
4. **Schedule Check**: Validates against staff working hours
5. **Bookly Scheduler**: Uses Bookly's scheduler for final validation

## Customization

### Styling

The CSS uses CSS custom properties for easy theming:

```css
.bookly-custom-search-form {
    --primary-color: #3498db;
    --success-color: #27ae60;
    --border-radius: 8px;
    --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}
```

### JavaScript Events

You can listen to custom events:

```javascript
$(document).on('bookly_search_completed', function(event, data) {
    console.log('Search completed:', data);
});

$(document).on('bookly_service_selected', function(event, serviceData) {
    console.log('Service selected:', serviceData);
});
```

## Troubleshooting

### Common Issues

1. **No results showing**: Check if services have matching durations
2. **Booking continuation fails**: Ensure Bookly form shortcode exists on a page
3. **Styling issues**: Check for CSS conflicts with theme

### Debug Mode

Add this to your wp-config.php for debugging:

```php
define('BOOKLY_CUSTOM_SEARCH_DEBUG', true);
```

## Requirements

- WordPress 5.0+
- Bookly Pro plugin
- PHP 7.4+
- jQuery (included with WordPress)

## Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- Mobile browsers (iOS Safari, Chrome Mobile)
