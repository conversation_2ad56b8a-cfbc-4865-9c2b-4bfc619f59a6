# Installation Guide - Bookly Custom Search Form

## Prerequisites

- WordPress 5.0 or higher
- Bookly Pro plugin installed and activated
- PHP 7.4 or higher

## Installation Steps

### 1. Copy Files

The custom search form module should already be integrated into your Bookly Pro installation. If you need to install it manually:

1. Copy the entire `custom_search_form` folder to:
   ```
   /wp-content/plugins/bookly-addon-pro/frontend/modules/
   ```

2. Ensure the folder structure looks like:
   ```
   bookly-addon-pro/
   ├── frontend/
   │   └── modules/
   │       └── custom_search_form/
   │           ├── Module.php
   │           ├── Ajax.php
   │           ├── templates/
   │           ├── resources/
   │           └── README.md
   ```

### 2. Activate the Module

The module is automatically loaded when Bookly Pro is active. No additional activation is required.

### 3. Create a Test Page

1. Go to WordPress Admin → Pages → Add New
2. Create a new page titled "Room Booking"
3. Add the shortcode: `[bookly-custom-search-form]`
4. Publish the page

### 4. Configure Bookly Services

For the custom search to work properly, ensure your Bookly setup includes:

1. **Services**: Create services with different durations (2h, 3h, 4h, etc.)
2. **Staff**: Set up staff members (representing rooms)
3. **Staff-Service Assignments**: Assign staff to services
4. **Working Hours**: Configure staff working schedules
5. **Prices**: Set prices for each staff-service combination

### 5. Test the Installation

1. Visit your test page
2. Select a date, time, and duration
3. Click "Search Rooms"
4. Verify that available rooms are displayed
5. Test selecting a room and continuing to booking

## Configuration Examples

### Basic Setup
```
[bookly-custom-search-form]
```

### Custom Configuration
```
[bookly-custom-search-form 
    title="Book Your Room" 
    available_durations="2,3,4,6,8" 
    min_time="08:00" 
    max_time="22:00"
    theme="compact"
]
```

## Troubleshooting

### No Search Results

**Problem**: Search returns no available rooms

**Solutions**:
1. Check if services exist with matching durations
2. Verify staff are assigned to services
3. Ensure staff have working hours configured
4. Check for existing appointments that might conflict

### JavaScript Errors

**Problem**: Form doesn't work, console shows errors

**Solutions**:
1. Check if jQuery is loaded
2. Verify Bookly Pro is active
3. Check for theme conflicts
4. Clear cache if using caching plugins

### Styling Issues

**Problem**: Form looks broken or unstyled

**Solutions**:
1. Check for CSS conflicts with theme
2. Try different theme variations (compact, minimal)
3. Add custom CSS to override conflicts

### AJAX Errors

**Problem**: Search fails with network errors

**Solutions**:
1. Check WordPress debug log
2. Verify AJAX URL is correct
3. Check server error logs
4. Test with different browsers

## Debug Mode

Enable debug mode by adding this to wp-config.php:

```php
define('BOOKLY_CUSTOM_SEARCH_DEBUG', true);
```

This will:
- Log search attempts to error log
- Show debug information in AJAX responses
- Add performance counters

## Support

If you encounter issues:

1. Check the browser console for JavaScript errors
2. Enable WordPress debug logging
3. Test with a default theme
4. Verify Bookly Pro is up to date

## File Permissions

Ensure proper file permissions:
- Folders: 755
- PHP files: 644
- CSS/JS files: 644

## Security Notes

- All AJAX requests are nonce-protected
- Input validation is performed on all parameters
- SQL queries use Bookly's ORM for security
- No direct database access is used

## Performance Tips

- Use caching plugins carefully (may cache AJAX responses)
- Consider limiting available_durations for better performance
- Monitor debug counters to optimize queries
- Test with realistic data volumes
